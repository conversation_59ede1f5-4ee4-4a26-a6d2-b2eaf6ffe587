Running customer-api...
{"level":"info","msg":"Starting Customer API","time":"2025-06-04T09:52:50+07:00"}
{"level":"info","msg":"Using DATABASE_URL for database connection","time":"2025-06-04T09:52:50+07:00"}
{"level":"info","msg":"Database connection established","time":"2025-06-04T09:52:51+07:00"}
[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

[GIN-debug] GET    /health                   --> customer-backend/internal/api/routes.SetupRoutes.func1 (6 handlers)
[GIN-debug] GET    /api/v1/shops             --> customer-backend/internal/api/handlers.(*ShopHandler).GetShops-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/search      --> customer-backend/internal/api/handlers.(*ShopHandler).SearchShops-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/popular     --> customer-backend/internal/api/handlers.(*ShopHandler).GetPopularShops-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/nearby      --> customer-backend/internal/api/handlers.(*ShopHandler).GetNearbyShops-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/category/:category --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopsByCategory-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/filter-options --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopFilterOptions-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/:id         --> customer-backend/internal/api/handlers.(*ShopHandler).GetShop-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/:id/status  --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopStatus-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/:id/menu    --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuItems-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/:id/menu/popular --> customer-backend/internal/api/handlers.(*MenuHandler).GetPopularItems-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/:id/menu/new --> customer-backend/internal/api/handlers.(*MenuHandler).GetNewItems-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/:id/menu/vegetarian --> customer-backend/internal/api/handlers.(*MenuHandler).GetVegetarianItems-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/:id/menu/search --> customer-backend/internal/api/handlers.(*MenuHandler).SearchMenuItems-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/:id/menu/categories --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuCategories-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/:id/menu/categories/:categoryId --> customer-backend/internal/api/handlers.(*MenuHandler).GetItemsByCategory-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/slug/:slug  --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopBySlug-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/slug/:slug/status --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopStatusBySlug-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/slug/:slug/menu --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/slug/:slug/menu/popular --> customer-backend/internal/api/handlers.(*MenuHandler).GetPopularItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/slug/:slug/menu/new --> customer-backend/internal/api/handlers.(*MenuHandler).GetNewItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/slug/:slug/menu/vegetarian --> customer-backend/internal/api/handlers.(*MenuHandler).GetVegetarianItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/slug/:slug/menu/search --> customer-backend/internal/api/handlers.(*MenuHandler).SearchMenuItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/slug/:slug/menu/categories --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuCategoriesBySlug-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/slug/:slug/menu/categories/:categoryId --> customer-backend/internal/api/handlers.(*MenuHandler).GetItemsByCategoryBySlug-fm (6 handlers)
[GIN-debug] GET    /api/v1/menu/items/:itemId --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuItem-fm (6 handlers)
[GIN-debug] GET    /docs/*any                --> customer-backend/internal/api/routes.SetupRoutes.func2 (6 handlers)
{"level":"info","msg":"Customer API starting on port 8900","time":"2025-06-04T09:52:51+07:00"}
{"level":"info","msg":"Getting shops with filters: page=1, limit=20","time":"2025-06-04T09:52:59+07:00"}
{"level":"info","msg":"::1 - [Wed, 04 Jun 2025 09:52:59 +07] \"GET /api/v1/shops/filter-options HTTP/1.1 200 398.75µs \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36\" \"\n","time":"2025-06-04T09:52:59+07:00"}
{"level":"info","msg":"::1 - [Wed, 04 Jun 2025 09:52:59 +07] \"GET /api/v1/shops HTTP/1.1 200 179.583917ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36\" \"\n","time":"2025-06-04T09:52:59+07:00"}
{"level":"info","msg":"Getting shops with filters: page=1, limit=20","time":"2025-06-04T09:53:04+07:00"}
{"level":"info","msg":"::1 - [Wed, 04 Jun 2025 09:53:04 +07] \"GET /api/v1/shops HTTP/1.1 200 104.970125ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36\" \"\n","time":"2025-06-04T09:53:04+07:00"}
{"level":"info","msg":"Getting shops with filters: page=1, limit=20","time":"2025-06-04T09:53:15+07:00"}
{"level":"info","msg":"::1 - [Wed, 04 Jun 2025 09:53:15 +07] \"GET /api/v1/shops HTTP/1.1 200 107.270042ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36\" \"\n","time":"2025-06-04T09:53:15+07:00"}
{"level":"info","msg":"Getting shops with filters: page=1, limit=20","time":"2025-06-04T09:53:20+07:00"}
{"level":"info","msg":"::1 - [Wed, 04 Jun 2025 09:53:20 +07] \"GET /api/v1/shops HTTP/1.1 200 104.615625ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36\" \"\n","time":"2025-06-04T09:53:20+07:00"}
{"level":"info","msg":"Getting shops with filters: page=1, limit=20","time":"2025-06-04T09:53:22+07:00"}
{"level":"info","msg":"::1 - [Wed, 04 Jun 2025 09:53:22 +07] \"GET /api/v1/shops HTTP/1.1 200 108.118083ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36\" \"\n","time":"2025-06-04T09:53:22+07:00"}
{"level":"info","msg":"Getting shops with filters: page=1, limit=20","time":"2025-06-04T09:54:04+07:00"}
{"level":"info","msg":"::1 - [Wed, 04 Jun 2025 09:54:04 +07] \"GET /api/v1/shops HTTP/1.1 200 103.192ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36\" \"\n","time":"2025-06-04T09:54:04+07:00"}
{"level":"info","msg":"Getting shops with filters: page=1, limit=20","time":"2025-06-04T09:54:05+07:00"}
{"level":"info","msg":"::1 - [Wed, 04 Jun 2025 09:54:05 +07] \"GET /api/v1/shops HTTP/1.1 200 70.195041ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36\" \"\n","time":"2025-06-04T09:54:05+07:00"}
{"level":"info","msg":"Shutting down server...","time":"2025-06-04T09:54:23+07:00"}
{"level":"info","msg":"Customer API exited","time":"2025-06-04T09:54:23+07:00"}
make[3]: *** [run] Error 1
