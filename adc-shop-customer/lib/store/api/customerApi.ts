import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'
import { getSession } from 'next-auth/react'
import type {
  CustomerResponse,
  CustomerShopSettings,
  CustomerMenuItem,
  MenuCategory,
  ShopFilters,
  MenuFilters,
} from '@/lib/services/customerApiClient'

// Base query with authentication
const baseQuery = fetchBaseQuery({
  baseUrl: '/api/services',
  prepareHeaders: async (headers, { getState }) => {
    // Get session for authentication
    const session = await getSession()

    if (session?.user) {
      headers.set('X-User-ID', session.user.id)
      headers.set('X-User-Email', session.user.email)
      headers.set('X-User-Role', session.user.role || 'customer')
      headers.set('X-Auth-Source', 'nextauth')
    }

    return headers
  },
})

export const customerApi = createApi({
  reducerPath: 'customerApi',
  baseQuery,
  tagTypes: ['Shop', 'MenuItem', 'MenuCategory'],
  endpoints: (builder) => ({
    // Shop endpoints (Backend determines if authentication is required)
    getShops: builder.query<CustomerResponse<{ shops: CustomerShopSettings[] }>, ShopFilters>({
      query: (filters = {}) => {
        const params = new URLSearchParams()
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            if (Array.isArray(value)) {
              value.forEach(item => params.append(key, item.toString()))
            } else {
              params.append(key, value.toString())
            }
          }
        })
        return `shops?${params.toString()}`
      },
      providesTags: ['Shop'],
    }),

    getShop: builder.query<CustomerResponse<{ shop: CustomerShopSettings }>, string>({
      query: (shopId) => `shops/${shopId}`,
      providesTags: (result, error, shopId) => [{ type: 'Shop', id: shopId }],
    }),

    searchShops: builder.query<CustomerResponse<{ shops: CustomerShopSettings[] }>, { query: string; filters?: ShopFilters }>({
      query: ({ query, filters = {} }) => {
        const params = new URLSearchParams({ q: query })
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            if (Array.isArray(value)) {
              value.forEach(item => params.append(key, item.toString()))
            } else {
              params.append(key, value.toString())
            }
          }
        })
        return `shops/search?${params.toString()}`
      },
      providesTags: ['Shop'],
    }),

    getPopularShops: builder.query<CustomerResponse<{ shops: CustomerShopSettings[] }>, ShopFilters>({
      query: (filters = {}) => {
        const params = new URLSearchParams()
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            if (Array.isArray(value)) {
              value.forEach(item => params.append(key, item.toString()))
            } else {
              params.append(key, value.toString())
            }
          }
        })
        return `shops/popular?${params.toString()}`
      },
      providesTags: ['Shop'],
    }),

    getNearbyShops: builder.query<CustomerResponse<{ shops: CustomerShopSettings[] }>, {
      latitude: number;
      longitude: number;
      radius?: number;
      filters?: ShopFilters;
    }>({
      query: ({ latitude, longitude, radius = 5, filters = {} }) => {
        const params = new URLSearchParams({
          latitude: latitude.toString(),
          longitude: longitude.toString(),
          radius: radius.toString()
        })
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            if (Array.isArray(value)) {
              value.forEach(item => params.append(key, item.toString()))
            } else {
              params.append(key, value.toString())
            }
          }
        })
        return `shops/nearby?${params.toString()}`
      },
      providesTags: ['Shop'],
    }),

    getShopsByCategory: builder.query<CustomerResponse<{ shops: CustomerShopSettings[] }>, { category: string; filters?: ShopFilters }>({
      query: ({ category, filters = {} }) => {
        const params = new URLSearchParams()
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            if (Array.isArray(value)) {
              value.forEach(item => params.append(key, item.toString()))
            } else {
              params.append(key, value.toString())
            }
          }
        })
        return `shops/category/${category}?${params.toString()}`
      },
      providesTags: ['Shop'],
    }),

    getShopStatus: builder.query<CustomerResponse<{ is_open: boolean }>, string>({
      query: (shopId) => `shops/${shopId}/status`,
      providesTags: (result, error, shopId) => [{ type: 'Shop', id: `${shopId}-status` }],
    }),

    // Menu endpoints (Backend determines if authentication is required)
    getMenuItems: builder.query<CustomerResponse<{ items: CustomerMenuItem[]; categories: MenuCategory[] }>, {
      shopId: string;
      filters?: MenuFilters;
    }>({
      query: ({ shopId, filters = {} }) => {
        const params = new URLSearchParams()
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            if (Array.isArray(value)) {
              value.forEach(item => params.append(key, item.toString()))
            } else {
              params.append(key, value.toString())
            }
          }
        })
        return `shops/${shopId}/menu?${params.toString()}`
      },
      providesTags: (result, error, { shopId }) => [
        { type: 'MenuItem', id: shopId },
        { type: 'MenuCategory', id: shopId },
      ],
    }),

    getMenuItem: builder.query<CustomerResponse<{ item: CustomerMenuItem }>, string>({
      query: (itemId) => `menu/items/${itemId}`,
      providesTags: (result, error, itemId) => [{ type: 'MenuItem', id: itemId }],
    }),

    getPopularMenuItems: builder.query<CustomerResponse<{ items: CustomerMenuItem[] }>, {
      shopId: string;
      filters?: MenuFilters;
    }>({
      query: ({ shopId, filters = {} }) => {
        const params = new URLSearchParams()
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            if (Array.isArray(value)) {
              value.forEach(item => params.append(key, item.toString()))
            } else {
              params.append(key, value.toString())
            }
          }
        })
        return `shops/${shopId}/menu/popular?${params.toString()}`
      },
      providesTags: (result, error, { shopId }) => [{ type: 'MenuItem', id: `${shopId}-popular` }],
    }),

    // Menu endpoints using shop slug
    getMenuItemsBySlug: builder.query<CustomerResponse<{ items: CustomerMenuItem[]; categories: MenuCategory[] }>, {
      shopSlug: string;
      filters?: MenuFilters;
    }>({
      query: ({ shopSlug, filters = {} }) => {
        const params = new URLSearchParams()
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            if (Array.isArray(value)) {
              value.forEach(item => params.append(key, item.toString()))
            } else {
              params.append(key, value.toString())
            }
          }
        })
        return `shops/slug/${shopSlug}/menu?${params.toString()}`
      },
      providesTags: (result, error, { shopSlug }) => [
        { type: 'MenuItem', id: shopSlug },
        { type: 'MenuCategory', id: shopSlug },
      ],
    }),

    getNewMenuItems: builder.query<CustomerResponse<{ items: CustomerMenuItem[] }>, {
      shopId: string;
      filters?: MenuFilters;
    }>({
      query: ({ shopId, filters = {} }) => {
        const params = new URLSearchParams()
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            if (Array.isArray(value)) {
              value.forEach(item => params.append(key, item.toString()))
            } else {
              params.append(key, value.toString())
            }
          }
        })
        return `shops/${shopId}/menu/new?${params.toString()}`
      },
      providesTags: (result, error, { shopId }) => [{ type: 'MenuItem', id: `${shopId}-new` }],
    }),

    getVegetarianItems: builder.query<CustomerResponse<{ items: CustomerMenuItem[] }>, {
      shopId: string;
      filters?: MenuFilters;
    }>({
      query: ({ shopId, filters = {} }) => {
        const params = new URLSearchParams()
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            if (Array.isArray(value)) {
              value.forEach(item => params.append(key, item.toString()))
            } else {
              params.append(key, value.toString())
            }
          }
        })
        return `shops/${shopId}/menu/vegetarian?${params.toString()}`
      },
      providesTags: (result, error, { shopId }) => [{ type: 'MenuItem', id: `${shopId}-vegetarian` }],
    }),

    searchMenuItems: builder.query<CustomerResponse<{ items: CustomerMenuItem[] }>, {
      shopId: string;
      query: string;
      filters?: MenuFilters;
    }>({
      query: ({ shopId, query, filters = {} }) => {
        const params = new URLSearchParams({ q: query })
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            if (Array.isArray(value)) {
              value.forEach(item => params.append(key, item.toString()))
            } else {
              params.append(key, value.toString())
            }
          }
        })
        return `shops/${shopId}/menu/search?${params.toString()}`
      },
      providesTags: (result, error, { shopId }) => [{ type: 'MenuItem', id: `${shopId}-search` }],
    }),

    getMenuCategories: builder.query<CustomerResponse<{ categories: MenuCategory[] }>, string>({
      query: (shopId) => `shops/${shopId}/menu/categories`,
      providesTags: (result, error, shopId) => [{ type: 'MenuCategory', id: shopId }],
    }),

    getItemsByCategory: builder.query<CustomerResponse<{ items: CustomerMenuItem[] }>, {
      shopId: string;
      categoryId: string;
      filters?: MenuFilters;
    }>({
      query: ({ shopId, categoryId, filters = {} }) => {
        const params = new URLSearchParams()
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            if (Array.isArray(value)) {
              value.forEach(item => params.append(key, item.toString()))
            } else {
              params.append(key, value.toString())
            }
          }
        })
        return `shops/${shopId}/menu/categories/${categoryId}?${params.toString()}`
      },
      providesTags: (result, error, { shopId, categoryId }) => [
        { type: 'MenuItem', id: `${shopId}-${categoryId}` },
      ],
    }),

    // Get shop filter options
    getShopFilterOptions: builder.query<CustomerResponse<{
      cuisine_types: string[];
      price_ranges: string[];
      features: string[];
    }>, void>({
      query: () => 'shops/filter-options',
      providesTags: ['Shop'],
    }),
  }),
})

// Export hooks for usage in functional components
export const {
  useGetShopsQuery,
  useGetShopQuery,
  useSearchShopsQuery,
  useGetPopularShopsQuery,
  useGetNearbyShopsQuery,
  useGetShopsByCategoryQuery,
  useGetShopStatusQuery,
  useGetMenuItemsQuery,
  useGetMenuItemsBySlugQuery,
  useGetMenuItemQuery,
  useGetPopularMenuItemsQuery,
  useGetNewMenuItemsQuery,
  useGetVegetarianItemsQuery,
  useSearchMenuItemsQuery,
  useGetMenuCategoriesQuery,
  useGetItemsByCategoryQuery,
  useGetShopFilterOptionsQuery,
} = customerApi
