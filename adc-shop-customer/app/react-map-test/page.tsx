'use client';

import GoogleMapComponent from '@/components/GoogleMapReact';
import type { ShopLocation } from '@/components/GoogleMapReact';

// Test data
const testShops: ShopLocation[] = [
  {
    id: 'test-1',
    name: 'Bangkok Noodle House',
    description: 'Authentic Thai noodles and street food',
    address: '123 Sukhumvit Road, Bangkok',
    lat: 13.7563,
    lng: 100.5018,
    rating: 4.5,
    priceRange: '$$',
    cuisineType: 'Thai',
    isOpen: true,
  },
  {
    id: 'test-2',
    name: 'Siam Fusion Restaurant',
    description: 'Modern Thai cuisine with international influences',
    address: '456 Silom Road, Bangkok',
    lat: 13.7440,
    lng: 100.5255,
    rating: 4.8,
    priceRange: '$$$',
    cuisineType: 'Fusion',
    isOpen: true,
  },
  {
    id: 'test-3',
    name: 'Street Food Paradise',
    description: 'Traditional Thai street food experience',
    address: '789 Chatuchak Market, Bangkok',
    lat: 13.7650,
    lng: 100.5380,
    rating: 4.2,
    priceRange: '$',
    cuisineType: 'Street Food',
    isOpen: false,
  },
];

export default function ReactMapTestPage() {
  const handleShopClick = (shop: ShopLocation) => {
    console.log('Shop clicked:', shop);
    alert(`Clicked on ${shop.name}\nRating: ${shop.rating}\nCuisine: ${shop.cuisineType}`);
  };

  const handleMapClick = (lat: number, lng: number) => {
    console.log('Map clicked at:', lat, lng);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Google Map React Test</h1>
        
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Interactive Map with google-map-react</h2>
          <div className="w-full h-96 border border-gray-300 rounded-lg overflow-hidden">
            <GoogleMapComponent
              shops={testShops}
              center={{ lat: 13.7563, lng: 100.5018 }}
              zoom={13}
              height="384px"
              width="100%"
              showUserLocation={true}
              onShopClick={handleShopClick}
              onMapClick={handleMapClick}
              className="w-full h-full rounded-lg"
            />
          </div>
          
          <div className="mt-4 text-sm text-gray-600">
            <p>✅ Using google-map-react library</p>
            <p>✅ {testShops.length} test restaurants with interactive markers</p>
            <p>✅ User location detection enabled</p>
            <p>✅ Click on markers to see restaurant details</p>
            <p>API Key: {process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY ? 'Configured' : 'Not configured'}</p>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold mb-4">Test Restaurants</h3>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {testShops.map((shop) => (
              <div key={shop.id} className="border border-gray-200 rounded-lg p-4">
                <h4 className="font-semibold text-gray-900">{shop.name}</h4>
                <p className="text-sm text-gray-600 mt-1">{shop.description}</p>
                <div className="flex items-center gap-2 mt-2 text-sm">
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">
                    {shop.cuisineType}
                  </span>
                  <span className="bg-green-100 text-green-800 px-2 py-1 rounded">
                    {shop.priceRange}
                  </span>
                  <span className={`px-2 py-1 rounded ${
                    shop.isOpen 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {shop.isOpen ? 'Open' : 'Closed'}
                  </span>
                </div>
                <p className="text-xs text-gray-500 mt-2">{shop.address}</p>
                <p className="text-xs text-gray-400 mt-1">
                  📍 {shop.lat.toFixed(4)}, {shop.lng.toFixed(4)}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
